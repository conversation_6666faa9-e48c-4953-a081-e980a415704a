// RemainingDebtsController.cs
using Business.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Entities.DTOs;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RemainingDebtsController : ControllerBase
    {
        private readonly IRemainingDebtService _remainingDebtService;

        public RemainingDebtsController(IRemainingDebtService remainingDebtService)
        {
            _remainingDebtService = remainingDebtService;
        }

        [HttpGet("getremainingdebtdetails")]
        public IActionResult GetRemainingDebtDetails()
        {
            var result = _remainingDebtService.GetRemainingDebtDetails();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpPost("adddebtpayment")]
        public IActionResult AddDebtPayment([FromBody] DebtPaymentDto debtPaymentDto)
        {
            var result = _remainingDebtService.AddDebtPayment(debtPaymentDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpDelete("delete")]
        public IActionResult Delete(int id)
        {
            var result = _remainingDebtService.Delete(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}