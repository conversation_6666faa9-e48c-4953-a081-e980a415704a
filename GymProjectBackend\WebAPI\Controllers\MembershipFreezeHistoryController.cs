using Business.Abstract;
using Entities.Concrete;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class MembershipFreezeHistoryController : ControllerBase
    {
        private readonly IMembershipFreezeHistoryService _freezeHistoryService;

        public MembershipFreezeHistoryController(IMembershipFreezeHistoryService freezeHistoryService)
        {
            _freezeHistoryService = freezeHistoryService;
        }

        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _freezeHistoryService.GetAll();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getbymembershipid/{membershipId}")]
        public IActionResult GetByMembershipId(int membershipId)
        {
            var result = _freezeHistoryService.GetByMembershipId(membershipId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getremainingfreezedays/{membershipId}")]
        public IActionResult GetRemainingFreezeDays(int membershipId)
        {
            var result = _freezeHistoryService.GetRemainingFreezeDays(membershipId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpPost("add")]
        public IActionResult Add(MembershipFreezeHistory history)
        {
            var result = _freezeHistoryService.Add(history);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpPost("update")]
        public IActionResult Update(MembershipFreezeHistory history)
        {
            var result = _freezeHistoryService.Update(history);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}